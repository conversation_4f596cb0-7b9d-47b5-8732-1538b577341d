# %%NBQA-CELL-SEP8f74c3
from autogen_agentchat.conditions import MaxMessageTermination, StopMessageTermination

max_termination = MaxMessageTermination(5)
stop_termination = StopMessageTermination()

or_termination = max_termination | stop_termination

or_term_config = or_termination.dump_component()
print("Config: ", or_term_config.model_dump_json())

new_or_termination = or_termination.load_component(or_term_config)


# %%NBQA-CELL-SEP8f74c3
from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient

# Create an agent that uses the OpenAI GPT-4o model.
model_client = OpenAIChatCompletionClient(
    model="gpt-4o",
    # api_key="YOUR_API_KEY",
)
agent = AssistantAgent(
    name="assistant",
    model_client=model_client,
    handoffs=["flights_refunder", "user"],
    # tools=[], # serializing tools is not yet supported
    system_message="Use tools to solve tasks.",
)
user_proxy = UserProxyAgent(name="user")


# %%NBQA-CELL-SEP8f74c3
user_proxy_config = user_proxy.dump_component()  # dump component
print(user_proxy_config.model_dump_json())
up_new = user_proxy.load_component(user_proxy_config)  # load component


# %%NBQA-CELL-SEP8f74c3
agent_config = agent.dump_component()  # dump component
print(agent_config.model_dump_json())
agent_new = agent.load_component(agent_config)  # load component


# %%NBQA-CELL-SEP8f74c3
from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_agentchat.conditions import MaxMessageTermination
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_ext.models.openai import OpenAIChatCompletionClient

# Create an agent that uses the OpenAI GPT-4o model.
model_client = OpenAIChatCompletionClient(
    model="gpt-4o",
    # api_key="YOUR_API_KEY",
)
agent = AssistantAgent(
    name="assistant",
    model_client=model_client,
    handoffs=["flights_refunder", "user"],
    # tools=[], # serializing tools is not yet supported
    system_message="Use tools to solve tasks.",
)

team = RoundRobinGroupChat(participants=[agent], termination_condition=MaxMessageTermination(2))

team_config = team.dump_component()  # dump component
print(team_config.model_dump_json())

await model_client.close()
