# %%NBQA-CELL-SEP8f74c3
from autogen_agentchat.messages import TextMessage

text_message = TextMessage(content="Hello, world!", source="User")


# %%NBQA-CELL-SEP8f74c3
from io import BytesIO

import requests
from autogen_agentchat.messages import MultiModalMessage
from autogen_core import Image as AGImage
from PIL import Image

pil_image = Image.open(BytesIO(requests.get("https://picsum.photos/300/200").content))
img = AGImage(pil_image)
multi_modal_message = MultiModalMessage(content=["Can you describe the content of this image?", img], source="User")
img
