# %%NBQA-CELL-SEP8f74c3
import asyncio
from dataclasses import dataclass

from autogen_core import (
    <PERSON>losureAgent,
    ClosureContext,
    DefaultSubscription,
    DefaultTopicId,
    MessageContext,
    SingleThreadedAgentRuntime,
)


# %%NBQA-CELL-SEP8f74c3
@dataclass
class FinalResult:
    value: str


# %%NBQA-CELL-SEP8f74c3
queue = asyncio.Queue[FinalResult]()


# %%NBQA-CELL-SEP8f74c3
async def output_result(_agent: ClosureContext, message: FinalResult, ctx: MessageContext) -> None:
    await queue.put(message)


# %%NBQA-CELL-SEP8f74c3
runtime = SingleThreadedAgentRuntime()
await ClosureAgent.register_closure(
    runtime, "output_result", output_result, subscriptions=lambda: [DefaultSubscription()]
)


# %%NBQA-CELL-SEP8f74c3
runtime.start()
await runtime.publish_message(FinalResult("Result 1"), DefaultTopicId())
await runtime.publish_message(FinalResult("Result 2"), DefaultTopicId())
await runtime.stop_when_idle()


# %%NBQA-CELL-SEP8f74c3
while not queue.empty():
    print((result := await queue.get()).value)
