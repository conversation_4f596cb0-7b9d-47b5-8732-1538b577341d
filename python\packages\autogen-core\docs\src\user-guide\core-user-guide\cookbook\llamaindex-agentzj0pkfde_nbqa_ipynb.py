# %%NBQA-CELL-SEP8f74c3
# pip install "llama-index-readers-web" "llama-index-readers-wikipedia" "llama-index-tools-wikipedia" "llama-index-embeddings-azure-openai" "llama-index-llms-azure-openai" "llama-index" "azure-identity"


# %%NBQA-CELL-SEP8f74c3
import os
from typing import List, Optional

from autogen_core import AgentId, MessageContext, RoutedAgent, SingleThreadedAgentRuntime, message_handler
from azure.identity import DefaultAzureCredential, get_bearer_token_provider
from llama_index.core import Settings
from llama_index.core.agent import ReActAgent
from llama_index.core.agent.runner.base import AgentRunner
from llama_index.core.base.llms.types import (
    ChatMessage,
    MessageRole,
)
from llama_index.core.chat_engine.types import AgentChatResponse
from llama_index.core.memory import ChatSummaryMemoryBuffer
from llama_index.core.memory.types import BaseMemory
from llama_index.embeddings.azure_openai import AzureOpenAIEmbedding
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.azure_openai import AzureOpenAI
from llama_index.llms.openai import OpenAI
from llama_index.tools.wikipedia import WikipediaToolSpec
from pydantic import BaseModel


# %%NBQA-CELL-SEP8f74c3
class Resource(BaseModel):
    content: str
    node_id: str
    score: Optional[float] = None


class Message(BaseModel):
    content: str
    sources: Optional[List[Resource]] = None


# %%NBQA-CELL-SEP8f74c3
class LlamaIndexAgent(RoutedAgent):
    def __init__(self, description: str, llama_index_agent: AgentRunner, memory: BaseMemory | None = None) -> None:
        super().__init__(description)

        self._llama_index_agent = llama_index_agent
        self._memory = memory

    @message_handler
    async def handle_user_message(self, message: Message, ctx: MessageContext) -> Message:
        # retriever history messages from memory!
        history_messages: List[ChatMessage] = []

        response: AgentChatResponse  # pyright: ignore
        if self._memory is not None:
            history_messages = self._memory.get(input=message.content)

            response = await self._llama_index_agent.achat(message=message.content, history_messages=history_messages)  # pyright: ignore
        else:
            response = await self._llama_index_agent.achat(message=message.content)  # pyright: ignore

        if isinstance(response, AgentChatResponse):
            if self._memory is not None:
                self._memory.put(ChatMessage(role=MessageRole.USER, content=message.content))
                self._memory.put(ChatMessage(role=MessageRole.ASSISTANT, content=response.response))

            assert isinstance(response.response, str)

            resources: List[Resource] = [
                Resource(content=source_node.get_text(), score=source_node.score, node_id=source_node.id_)
                for source_node in response.source_nodes
            ]

            tools: List[Resource] = [
                Resource(content=source.content, node_id=source.tool_name) for source in response.sources
            ]

            resources.extend(tools)
            return Message(content=response.response, sources=resources)
        else:
            return Message(content="I'm sorry, I don't have an answer for you.")


# %%NBQA-CELL-SEP8f74c3
# llm = AzureOpenAI(
#     deployment_name=os.getenv("AZURE_OPENAI_DEPLOYMENT"),
#     temperature=0.0,
#     azure_ad_token_provider = get_bearer_token_provider(DefaultAzureCredential()),
#     # api_key=os.getenv("AZURE_OPENAI_API_KEY"),
#     azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
#     api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
# )
llm = OpenAI(
    model="gpt-4o",
    temperature=0.0,
    api_key=os.getenv("OPENAI_API_KEY"),
)

# embed_model = AzureOpenAIEmbedding(
#     deployment_name=os.getenv("AZURE_OPENAI_EMBEDDING_MODEL"),
#     temperature=0.0,
#     azure_ad_token_provider = get_bearer_token_provider(DefaultAzureCredential()),
#     api_key=os.getenv("AZURE_OPENAI_API_KEY"),
#     azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
#     api_version=os.getenv("AZURE_OPENAI_API_VERSION"),
# )
embed_model = OpenAIEmbedding(
    model="text-embedding-ada-002",
    api_key=os.getenv("OPENAI_API_KEY"),
)

Settings.llm = llm
Settings.embed_model = embed_model


# %%NBQA-CELL-SEP8f74c3
wiki_spec = WikipediaToolSpec()
wikipedia_tool = wiki_spec.to_tool_list()[1]


# %%NBQA-CELL-SEP8f74c3
runtime = SingleThreadedAgentRuntime()
await LlamaIndexAgent.register(
    runtime,
    "chat_agent",
    lambda: LlamaIndexAgent(
        description="Llama Index Agent",
        llama_index_agent=ReActAgent.from_tools(
            tools=[wikipedia_tool],
            llm=llm,
            max_iterations=8,
            memory=ChatSummaryMemoryBuffer(llm=llm, token_limit=16000),
            verbose=True,
        ),
    ),
)
agent = AgentId("chat_agent", "default")


# %%NBQA-CELL-SEP8f74c3
runtime.start()


# %%NBQA-CELL-SEP8f74c3
message = Message(content="What are the best movies from studio Ghibli?")
response = await runtime.send_message(message, agent)
assert isinstance(response, Message)
print(response.content)


# %%NBQA-CELL-SEP8f74c3
if response.sources is not None:
    for source in response.sources:
        print(source.content)


# %%NBQA-CELL-SEP8f74c3
await runtime.stop()
