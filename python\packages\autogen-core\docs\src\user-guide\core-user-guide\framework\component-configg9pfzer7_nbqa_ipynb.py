# %%NBQA-CELL-SEP8f74c3
from autogen_core.models import ChatCompletionClient

config = {
    "provider": "openai_chat_completion_client",
    "config": {"model": "gpt-4o"},
}

client = ChatCompletionClient.load_component(config)


# %%NBQA-CELL-SEP8f74c3
from autogen_core import Component, ComponentBase
from pydantic import BaseModel


class Config(BaseModel):
    value: str


class MyComponent(ComponentBase[Config], Component[Config]):
    component_type = "custom"
    component_config_schema = Config

    def __init__(self, value: str):
        self.value = value

    def _to_config(self) -> Config:
        return Config(value=self.value)

    @classmethod
    def _from_config(cls, config: Config) -> "MyComponent":
        return cls(value=config.value)


# %%NBQA-CELL-SEP8f74c3
from pydantic import BaseModel, SecretStr


class ClientConfig(BaseModel):
    endpoint: str
    api_key: SecretStr
